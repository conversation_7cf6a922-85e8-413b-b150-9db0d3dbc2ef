{"ast": null, "code": "/**\n * Given a absolute or relative time definition and current/prev time state of the sequence,\n * calculate an absolute time for the next keyframes.\n */\nfunction calcNextTime(current, next, prev, labels) {\n  var _a;\n  if (typeof next === \"number\") {\n    return next;\n  } else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n    return Math.max(0, current + parseFloat(next));\n  } else if (next === \"<\") {\n    return prev;\n  } else {\n    return (_a = labels.get(next)) !== null && _a !== void 0 ? _a : current;\n  }\n}\nexport { calcNextTime };", "map": {"version": 3, "names": ["calcNextTime", "current", "next", "prev", "labels", "_a", "startsWith", "Math", "max", "parseFloat", "get"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs"], "sourcesContent": ["/**\n * Given a absolute or relative time definition and current/prev time state of the sequence,\n * calculate an absolute time for the next keyframes.\n */\nfunction calcNextTime(current, next, prev, labels) {\n    var _a;\n    if (typeof next === \"number\") {\n        return next;\n    }\n    else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n        return Math.max(0, current + parseFloat(next));\n    }\n    else if (next === \"<\") {\n        return prev;\n    }\n    else {\n        return (_a = labels.get(next)) !== null && _a !== void 0 ? _a : current;\n    }\n}\n\nexport { calcNextTime };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC/C,IAAIC,EAAE;EACN,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf,CAAC,MACI,IAAIA,IAAI,CAACI,UAAU,CAAC,GAAG,CAAC,IAAIJ,IAAI,CAACI,UAAU,CAAC,GAAG,CAAC,EAAE;IACnD,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,OAAO,GAAGQ,UAAU,CAACP,IAAI,CAAC,CAAC;EAClD,CAAC,MACI,IAAIA,IAAI,KAAK,GAAG,EAAE;IACnB,OAAOC,IAAI;EACf,CAAC,MACI;IACD,OAAO,CAACE,EAAE,GAAGD,MAAM,CAACM,GAAG,CAACR,IAAI,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGJ,OAAO;EAC3E;AACJ;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}