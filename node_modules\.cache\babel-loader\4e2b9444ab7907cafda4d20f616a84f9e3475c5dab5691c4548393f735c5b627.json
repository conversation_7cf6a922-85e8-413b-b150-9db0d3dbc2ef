{"ast": null, "code": "import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\nlet handoffFrameTime;\nfunction handoffOptimizedAppearAnimation(elementId, valueName,\n/**\n * Legacy arguments. This function is inlined as part of SSG so it can be there's\n * a version mismatch between the main included Motion and the inlined script.\n *\n * Remove in early 2024.\n */\n_value, _frame) {\n  const optimisedValueName = transformProps.has(valueName) ? \"transform\" : valueName;\n  const storeId = appearStoreId(elementId, optimisedValueName);\n  const optimisedAnimation = appearAnimationStore.get(storeId);\n  if (!optimisedAnimation) {\n    return null;\n  }\n  const {\n    animation,\n    startTime\n  } = optimisedAnimation;\n  const cancelAnimation = () => {\n    appearAnimationStore.delete(storeId);\n    try {\n      animation.cancel();\n    } catch (error) {}\n  };\n  /**\n   * If the startTime is null, this animation is the Paint Ready detection animation\n   * and we can cancel it immediately without handoff.\n   *\n   * Or if we've already handed off the animation then we're now interrupting it.\n   * In which case we need to cancel it.\n   */\n  if (startTime === null || window.HandoffComplete) {\n    cancelAnimation();\n    return null;\n  } else {\n    /**\n     * Otherwise we're handing off this animation to the main thread.\n     *\n     * Record the time of the first handoff. We call performance.now() once\n     * here and once in startOptimisedAnimation to ensure we're getting\n     * close to a frame-locked time. This keeps all animations in sync.\n     */\n    if (handoffFrameTime === undefined) {\n      handoffFrameTime = performance.now();\n    }\n    /**\n     * We use main thread timings vs those returned by Animation.currentTime as it\n     * can be the case, particularly in Firefox, that currentTime doesn't return\n     * an updated value for several frames, even as the animation plays smoothly via\n     * the GPU.\n     */\n    return handoffFrameTime - startTime || 0;\n  }\n}\nexport { handoffOptimizedAppearAnimation };", "map": {"version": 3, "names": ["transformProps", "appearAnimationStore", "appearStoreId", "handoffFrameTime", "handoffOptimizedAppearAnimation", "elementId", "valueName", "_value", "_frame", "optimisedValueName", "has", "storeId", "optimisedAnimation", "get", "animation", "startTime", "cancelAnimation", "delete", "cancel", "error", "window", "HandoffComplete", "undefined", "performance", "now"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/animation/optimized-appear/handoff.mjs"], "sourcesContent": ["import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\n\nlet handoffFrameTime;\nfunction handoffOptimizedAppearAnimation(elementId, valueName, \n/**\n * Legacy arguments. This function is inlined as part of SSG so it can be there's\n * a version mismatch between the main included Motion and the inlined script.\n *\n * Remove in early 2024.\n */\n_value, _frame) {\n    const optimisedValueName = transformProps.has(valueName)\n        ? \"transform\"\n        : valueName;\n    const storeId = appearStoreId(elementId, optimisedValueName);\n    const optimisedAnimation = appearAnimationStore.get(storeId);\n    if (!optimisedAnimation) {\n        return null;\n    }\n    const { animation, startTime } = optimisedAnimation;\n    const cancelAnimation = () => {\n        appearAnimationStore.delete(storeId);\n        try {\n            animation.cancel();\n        }\n        catch (error) { }\n    };\n    /**\n     * If the startTime is null, this animation is the Paint Ready detection animation\n     * and we can cancel it immediately without handoff.\n     *\n     * Or if we've already handed off the animation then we're now interrupting it.\n     * In which case we need to cancel it.\n     */\n    if (startTime === null || window.HandoffComplete) {\n        cancelAnimation();\n        return null;\n    }\n    else {\n        /**\n         * Otherwise we're handing off this animation to the main thread.\n         *\n         * Record the time of the first handoff. We call performance.now() once\n         * here and once in startOptimisedAnimation to ensure we're getting\n         * close to a frame-locked time. This keeps all animations in sync.\n         */\n        if (handoffFrameTime === undefined) {\n            handoffFrameTime = performance.now();\n        }\n        /**\n         * We use main thread timings vs those returned by Animation.currentTime as it\n         * can be the case, particularly in Firefox, that currentTime doesn't return\n         * an updated value for several frames, even as the animation plays smoothly via\n         * the GPU.\n         */\n        return handoffFrameTime - startTime || 0;\n    }\n}\n\nexport { handoffOptimizedAppearAnimation };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uCAAuC;AACtE,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,aAAa,QAAQ,gBAAgB;AAE9C,IAAIC,gBAAgB;AACpB,SAASC,+BAA+BA,CAACC,SAAS,EAAEC,SAAS;AAC7D;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,EAAEC,MAAM,EAAE;EACZ,MAAMC,kBAAkB,GAAGT,cAAc,CAACU,GAAG,CAACJ,SAAS,CAAC,GAClD,WAAW,GACXA,SAAS;EACf,MAAMK,OAAO,GAAGT,aAAa,CAACG,SAAS,EAAEI,kBAAkB,CAAC;EAC5D,MAAMG,kBAAkB,GAAGX,oBAAoB,CAACY,GAAG,CAACF,OAAO,CAAC;EAC5D,IAAI,CAACC,kBAAkB,EAAE;IACrB,OAAO,IAAI;EACf;EACA,MAAM;IAAEE,SAAS;IAAEC;EAAU,CAAC,GAAGH,kBAAkB;EACnD,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC1Bf,oBAAoB,CAACgB,MAAM,CAACN,OAAO,CAAC;IACpC,IAAI;MACAG,SAAS,CAACI,MAAM,CAAC,CAAC;IACtB,CAAC,CACD,OAAOC,KAAK,EAAE,CAAE;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIJ,SAAS,KAAK,IAAI,IAAIK,MAAM,CAACC,eAAe,EAAE;IAC9CL,eAAe,CAAC,CAAC;IACjB,OAAO,IAAI;EACf,CAAC,MACI;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIb,gBAAgB,KAAKmB,SAAS,EAAE;MAChCnB,gBAAgB,GAAGoB,WAAW,CAACC,GAAG,CAAC,CAAC;IACxC;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,OAAOrB,gBAAgB,GAAGY,SAAS,IAAI,CAAC;EAC5C;AACJ;AAEA,SAASX,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}