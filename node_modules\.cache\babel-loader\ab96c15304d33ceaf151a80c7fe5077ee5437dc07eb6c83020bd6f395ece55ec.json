{"ast": null, "code": "/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = str => str.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase();\nexport { camelToDash };", "map": {"version": 3, "names": ["camelToDash", "str", "replace", "toLowerCase"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs"], "sourcesContent": ["/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = (str) => str.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase();\n\nexport { camelToDash };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,WAAW,GAAIC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;AAElF,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}