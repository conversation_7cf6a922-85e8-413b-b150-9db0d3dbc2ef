{"ast": null, "code": "function shallowCompare(next, prev) {\n  if (!Array.isArray(prev)) return false;\n  const prevLength = prev.length;\n  if (prevLength !== next.length) return false;\n  for (let i = 0; i < prevLength; i++) {\n    if (prev[i] !== next[i]) return false;\n  }\n  return true;\n}\nexport { shallowCompare };", "map": {"version": 3, "names": ["shallowCompare", "next", "prev", "Array", "isArray", "prevLength", "length", "i"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/utils/shallow-compare.mjs"], "sourcesContent": ["function shallowCompare(next, prev) {\n    if (!Array.isArray(prev))\n        return false;\n    const prevLength = prev.length;\n    if (prevLength !== next.length)\n        return false;\n    for (let i = 0; i < prevLength; i++) {\n        if (prev[i] !== next[i])\n            return false;\n    }\n    return true;\n}\n\nexport { shallowCompare };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAChC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EACpB,OAAO,KAAK;EAChB,MAAMG,UAAU,GAAGH,IAAI,CAACI,MAAM;EAC9B,IAAID,UAAU,KAAKJ,IAAI,CAACK,MAAM,EAC1B,OAAO,KAAK;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;IACjC,IAAIL,IAAI,CAACK,CAAC,CAAC,KAAKN,IAAI,CAACM,CAAC,CAAC,EACnB,OAAO,KAAK;EACpB;EACA,OAAO,IAAI;AACf;AAEA,SAASP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}