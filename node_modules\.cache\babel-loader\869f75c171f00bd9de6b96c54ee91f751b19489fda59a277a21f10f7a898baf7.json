{"ast": null, "code": "const featureProps = {\n  animation: [\"animate\", \"variants\", \"whileHover\", \"whileTap\", \"exit\", \"whileInView\", \"whileFocus\", \"whileDrag\"],\n  exit: [\"exit\"],\n  drag: [\"drag\", \"dragControls\"],\n  focus: [\"whileFocus\"],\n  hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n  tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n  pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n  inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n  layout: [\"layout\", \"layoutId\"]\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n  featureDefinitions[key] = {\n    isEnabled: props => featureProps[key].some(name => !!props[name])\n  };\n}\nexport { featureDefinitions };", "map": {"version": 3, "names": ["featureProps", "animation", "exit", "drag", "focus", "hover", "tap", "pan", "inView", "layout", "featureDefinitions", "key", "isEnabled", "props", "some", "name"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/motion/features/definitions.mjs"], "sourcesContent": ["const featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\nexport { featureDefinitions };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,SAAS,EAAE,CACP,SAAS,EACT,UAAU,EACV,YAAY,EACZ,UAAU,EACV,MAAM,EACN,aAAa,EACb,YAAY,EACZ,WAAW,CACd;EACDC,IAAI,EAAE,CAAC,MAAM,CAAC;EACdC,IAAI,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;EAC9BC,KAAK,EAAE,CAAC,YAAY,CAAC;EACrBC,KAAK,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC;EACnDC,GAAG,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,CAAC;EACvDC,GAAG,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,CAAC;EAC7DC,MAAM,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;EAC7DC,MAAM,EAAE,CAAC,QAAQ,EAAE,UAAU;AACjC,CAAC;AACD,MAAMC,kBAAkB,GAAG,CAAC,CAAC;AAC7B,KAAK,MAAMC,GAAG,IAAIX,YAAY,EAAE;EAC5BU,kBAAkB,CAACC,GAAG,CAAC,GAAG;IACtBC,SAAS,EAAGC,KAAK,IAAKb,YAAY,CAACW,GAAG,CAAC,CAACG,IAAI,CAAEC,IAAI,IAAK,CAAC,CAACF,KAAK,CAACE,IAAI,CAAC;EACxE,CAAC;AACL;AAEA,SAASL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}