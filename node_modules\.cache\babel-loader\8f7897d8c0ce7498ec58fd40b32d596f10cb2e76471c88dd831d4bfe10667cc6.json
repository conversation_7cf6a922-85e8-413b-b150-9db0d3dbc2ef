{"ast": null, "code": "const isBrowser = typeof document !== \"undefined\";\nexport { isBrowser };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "document"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/utils/is-browser.mjs"], "sourcesContent": ["const isBrowser = typeof document !== \"undefined\";\n\nexport { isBrowser };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG,OAAOC,QAAQ,KAAK,WAAW;AAEjD,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}