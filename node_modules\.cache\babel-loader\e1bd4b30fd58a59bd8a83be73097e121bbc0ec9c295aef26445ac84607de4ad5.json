{"ast": null, "code": "import { mix } from '../mix.mjs';\nimport { progress } from '../progress.mjs';\nfunction fillOffset(offset, remaining) {\n  const min = offset[offset.length - 1];\n  for (let i = 1; i <= remaining; i++) {\n    const offsetProgress = progress(0, remaining, i);\n    offset.push(mix(min, 1, offsetProgress));\n  }\n}\nexport { fillOffset };", "map": {"version": 3, "names": ["mix", "progress", "fillOffset", "offset", "remaining", "min", "length", "i", "offsetProgress", "push"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/utils/offsets/fill.mjs"], "sourcesContent": ["import { mix } from '../mix.mjs';\nimport { progress } from '../progress.mjs';\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = progress(0, remaining, i);\n        offset.push(mix(min, 1, offsetProgress));\n    }\n}\n\nexport { fillOffset };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,YAAY;AAChC,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C,SAASC,UAAUA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACnC,MAAMC,GAAG,GAAGF,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,SAAS,EAAEG,CAAC,EAAE,EAAE;IACjC,MAAMC,cAAc,GAAGP,QAAQ,CAAC,CAAC,EAAEG,SAAS,EAAEG,CAAC,CAAC;IAChDJ,MAAM,CAACM,IAAI,CAACT,GAAG,CAACK,GAAG,EAAE,CAAC,EAAEG,cAAc,CAAC,CAAC;EAC5C;AACJ;AAEA,SAASN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}