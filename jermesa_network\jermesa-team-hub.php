<?php
/**
 * Plugin Name: Jermesa Studio Team Hub
 * Plugin URI: https://jermesa.com
 * Description: Comprehensive team messaging and file management application for Jermesa Studio
 * Version: 1.0.0
 * Author: Jermesa Studio
 * Author URI: https://jermesa.com
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('JERMESA_TEAM_HUB_VERSION', '1.0.0');
define('JERMESA_TEAM_HUB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('JERMESA_TEAM_HUB_PLUGIN_PATH', plugin_dir_path(__FILE__));

// Main plugin class
class JermesaTeamHub {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('jermesa_team_hub', array($this, 'render_app'));
    }
    
    public function init() {
        // Initialize plugin
    }
    
    public function enqueue_scripts() {
        wp_enqueue_script(
            'jermesa-team-hub-js',
            JERMESA_TEAM_HUB_PLUGIN_URL . 'static/js/main.js',
            array(),
            JERMESA_TEAM_HUB_VERSION,
            true
        );
        
        wp_enqueue_style(
            'jermesa-team-hub-css',
            JERMESA_TEAM_HUB_PLUGIN_URL . 'static/css/main.css',
            array(),
            JERMESA_TEAM_HUB_VERSION
        );
    }
    
    public function render_app($atts) {
        return '<div id="jermesa-team-hub-root"></div>';
    }
}

// Initialize plugin
new JermesaTeamHub();
?>