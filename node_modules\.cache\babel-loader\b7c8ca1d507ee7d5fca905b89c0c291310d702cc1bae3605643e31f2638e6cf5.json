{"ast": null, "code": "import { Feature } from '../Feature.mjs';\nimport { observeIntersection } from './observers.mjs';\nconst thresholdNames = {\n  some: 0,\n  all: 1\n};\nclass InViewFeature extends Feature {\n  constructor() {\n    super(...arguments);\n    this.hasEnteredView = false;\n    this.isInView = false;\n  }\n  startObserver() {\n    this.unmount();\n    const {\n      viewport = {}\n    } = this.node.getProps();\n    const {\n      root,\n      margin: rootMargin,\n      amount = \"some\",\n      once\n    } = viewport;\n    const options = {\n      root: root ? root.current : undefined,\n      rootMargin,\n      threshold: typeof amount === \"number\" ? amount : thresholdNames[amount]\n    };\n    const onIntersectionUpdate = entry => {\n      const {\n        isIntersecting\n      } = entry;\n      /**\n       * If there's been no change in the viewport state, early return.\n       */\n      if (this.isInView === isIntersecting) return;\n      this.isInView = isIntersecting;\n      /**\n       * Handle hasEnteredView. If this is only meant to run once, and\n       * element isn't visible, early return. Otherwise set hasEnteredView to true.\n       */\n      if (once && !isIntersecting && this.hasEnteredView) {\n        return;\n      } else if (isIntersecting) {\n        this.hasEnteredView = true;\n      }\n      if (this.node.animationState) {\n        this.node.animationState.setActive(\"whileInView\", isIntersecting);\n      }\n      /**\n       * Use the latest committed props rather than the ones in scope\n       * when this observer is created\n       */\n      const {\n        onViewportEnter,\n        onViewportLeave\n      } = this.node.getProps();\n      const callback = isIntersecting ? onViewportEnter : onViewportLeave;\n      callback && callback(entry);\n    };\n    return observeIntersection(this.node.current, options, onIntersectionUpdate);\n  }\n  mount() {\n    this.startObserver();\n  }\n  update() {\n    if (typeof IntersectionObserver === \"undefined\") return;\n    const {\n      props,\n      prevProps\n    } = this.node;\n    const hasOptionsChanged = [\"amount\", \"margin\", \"root\"].some(hasViewportOptionChanged(props, prevProps));\n    if (hasOptionsChanged) {\n      this.startObserver();\n    }\n  }\n  unmount() {}\n}\nfunction hasViewportOptionChanged({\n  viewport = {}\n}, {\n  viewport: prevViewport = {}\n} = {}) {\n  return name => viewport[name] !== prevViewport[name];\n}\nexport { InViewFeature };", "map": {"version": 3, "names": ["Feature", "observeIntersection", "thresholdNames", "some", "all", "InViewFeature", "constructor", "arguments", "hasEnteredView", "isInView", "startObserver", "unmount", "viewport", "node", "getProps", "root", "margin", "rootMargin", "amount", "once", "options", "current", "undefined", "threshold", "onIntersectionUpdate", "entry", "isIntersecting", "animationState", "setActive", "onViewportEnter", "onViewportLeave", "callback", "mount", "update", "IntersectionObserver", "props", "prevProps", "hasOptionsChanged", "hasViewportOptionChanged", "prevViewport", "name"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs"], "sourcesContent": ["import { Feature } from '../Feature.mjs';\nimport { observeIntersection } from './observers.mjs';\n\nconst thresholdNames = {\n    some: 0,\n    all: 1,\n};\nclass InViewFeature extends Feature {\n    constructor() {\n        super(...arguments);\n        this.hasEnteredView = false;\n        this.isInView = false;\n    }\n    startObserver() {\n        this.unmount();\n        const { viewport = {} } = this.node.getProps();\n        const { root, margin: rootMargin, amount = \"some\", once } = viewport;\n        const options = {\n            root: root ? root.current : undefined,\n            rootMargin,\n            threshold: typeof amount === \"number\" ? amount : thresholdNames[amount],\n        };\n        const onIntersectionUpdate = (entry) => {\n            const { isIntersecting } = entry;\n            /**\n             * If there's been no change in the viewport state, early return.\n             */\n            if (this.isInView === isIntersecting)\n                return;\n            this.isInView = isIntersecting;\n            /**\n             * Handle hasEnteredView. If this is only meant to run once, and\n             * element isn't visible, early return. Otherwise set hasEnteredView to true.\n             */\n            if (once && !isIntersecting && this.hasEnteredView) {\n                return;\n            }\n            else if (isIntersecting) {\n                this.hasEnteredView = true;\n            }\n            if (this.node.animationState) {\n                this.node.animationState.setActive(\"whileInView\", isIntersecting);\n            }\n            /**\n             * Use the latest committed props rather than the ones in scope\n             * when this observer is created\n             */\n            const { onViewportEnter, onViewportLeave } = this.node.getProps();\n            const callback = isIntersecting ? onViewportEnter : onViewportLeave;\n            callback && callback(entry);\n        };\n        return observeIntersection(this.node.current, options, onIntersectionUpdate);\n    }\n    mount() {\n        this.startObserver();\n    }\n    update() {\n        if (typeof IntersectionObserver === \"undefined\")\n            return;\n        const { props, prevProps } = this.node;\n        const hasOptionsChanged = [\"amount\", \"margin\", \"root\"].some(hasViewportOptionChanged(props, prevProps));\n        if (hasOptionsChanged) {\n            this.startObserver();\n        }\n    }\n    unmount() { }\n}\nfunction hasViewportOptionChanged({ viewport = {} }, { viewport: prevViewport = {} } = {}) {\n    return (name) => viewport[name] !== prevViewport[name];\n}\n\nexport { InViewFeature };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASC,mBAAmB,QAAQ,iBAAiB;AAErD,MAAMC,cAAc,GAAG;EACnBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE;AACT,CAAC;AACD,MAAMC,aAAa,SAASL,OAAO,CAAC;EAChCM,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,CAAC,CAAC;IACd,MAAM;MAAEC,QAAQ,GAAG,CAAC;IAAE,CAAC,GAAG,IAAI,CAACC,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC9C,MAAM;MAAEC,IAAI;MAAEC,MAAM,EAAEC,UAAU;MAAEC,MAAM,GAAG,MAAM;MAAEC;IAAK,CAAC,GAAGP,QAAQ;IACpE,MAAMQ,OAAO,GAAG;MACZL,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACM,OAAO,GAAGC,SAAS;MACrCL,UAAU;MACVM,SAAS,EAAE,OAAOL,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGhB,cAAc,CAACgB,MAAM;IAC1E,CAAC;IACD,MAAMM,oBAAoB,GAAIC,KAAK,IAAK;MACpC,MAAM;QAAEC;MAAe,CAAC,GAAGD,KAAK;MAChC;AACZ;AACA;MACY,IAAI,IAAI,CAAChB,QAAQ,KAAKiB,cAAc,EAChC;MACJ,IAAI,CAACjB,QAAQ,GAAGiB,cAAc;MAC9B;AACZ;AACA;AACA;MACY,IAAIP,IAAI,IAAI,CAACO,cAAc,IAAI,IAAI,CAAClB,cAAc,EAAE;QAChD;MACJ,CAAC,MACI,IAAIkB,cAAc,EAAE;QACrB,IAAI,CAAClB,cAAc,GAAG,IAAI;MAC9B;MACA,IAAI,IAAI,CAACK,IAAI,CAACc,cAAc,EAAE;QAC1B,IAAI,CAACd,IAAI,CAACc,cAAc,CAACC,SAAS,CAAC,aAAa,EAAEF,cAAc,CAAC;MACrE;MACA;AACZ;AACA;AACA;MACY,MAAM;QAAEG,eAAe;QAAEC;MAAgB,CAAC,GAAG,IAAI,CAACjB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjE,MAAMiB,QAAQ,GAAGL,cAAc,GAAGG,eAAe,GAAGC,eAAe;MACnEC,QAAQ,IAAIA,QAAQ,CAACN,KAAK,CAAC;IAC/B,CAAC;IACD,OAAOxB,mBAAmB,CAAC,IAAI,CAACY,IAAI,CAACQ,OAAO,EAAED,OAAO,EAAEI,oBAAoB,CAAC;EAChF;EACAQ,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACtB,aAAa,CAAC,CAAC;EACxB;EACAuB,MAAMA,CAAA,EAAG;IACL,IAAI,OAAOC,oBAAoB,KAAK,WAAW,EAC3C;IACJ,MAAM;MAAEC,KAAK;MAAEC;IAAU,CAAC,GAAG,IAAI,CAACvB,IAAI;IACtC,MAAMwB,iBAAiB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAClC,IAAI,CAACmC,wBAAwB,CAACH,KAAK,EAAEC,SAAS,CAAC,CAAC;IACvG,IAAIC,iBAAiB,EAAE;MACnB,IAAI,CAAC3B,aAAa,CAAC,CAAC;IACxB;EACJ;EACAC,OAAOA,CAAA,EAAG,CAAE;AAChB;AACA,SAAS2B,wBAAwBA,CAAC;EAAE1B,QAAQ,GAAG,CAAC;AAAE,CAAC,EAAE;EAAEA,QAAQ,EAAE2B,YAAY,GAAG,CAAC;AAAE,CAAC,GAAG,CAAC,CAAC,EAAE;EACvF,OAAQC,IAAI,IAAK5B,QAAQ,CAAC4B,IAAI,CAAC,KAAKD,YAAY,CAACC,IAAI,CAAC;AAC1D;AAEA,SAASnC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}