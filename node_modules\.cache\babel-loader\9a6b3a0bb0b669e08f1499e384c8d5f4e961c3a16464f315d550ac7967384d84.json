{"ast": null, "code": "import { isVariantLabel } from '../../render/utils/is-variant-label.mjs';\nimport { isControllingVariants } from '../../render/utils/is-controlling-variants.mjs';\nfunction getCurrentTreeVariants(props, context) {\n  if (isControllingVariants(props)) {\n    const {\n      initial,\n      animate\n    } = props;\n    return {\n      initial: initial === false || isVariantLabel(initial) ? initial : undefined,\n      animate: isVariantLabel(animate) ? animate : undefined\n    };\n  }\n  return props.inherit !== false ? context : {};\n}\nexport { getCurrentTreeVariants };", "map": {"version": 3, "names": ["isVariantLabel", "isControllingVariants", "getCurrentTreeVariants", "props", "context", "initial", "animate", "undefined", "inherit"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs"], "sourcesContent": ["import { isVariantLabel } from '../../render/utils/is-variant-label.mjs';\nimport { isControllingVariants } from '../../render/utils/is-controlling-variants.mjs';\n\nfunction getCurrentTreeVariants(props, context) {\n    if (isControllingVariants(props)) {\n        const { initial, animate } = props;\n        return {\n            initial: initial === false || isVariantLabel(initial)\n                ? initial\n                : undefined,\n            animate: isVariantLabel(animate) ? animate : undefined,\n        };\n    }\n    return props.inherit !== false ? context : {};\n}\n\nexport { getCurrentTreeVariants };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,yCAAyC;AACxE,SAASC,qBAAqB,QAAQ,gDAAgD;AAEtF,SAASC,sBAAsBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5C,IAAIH,qBAAqB,CAACE,KAAK,CAAC,EAAE;IAC9B,MAAM;MAAEE,OAAO;MAAEC;IAAQ,CAAC,GAAGH,KAAK;IAClC,OAAO;MACHE,OAAO,EAAEA,OAAO,KAAK,KAAK,IAAIL,cAAc,CAACK,OAAO,CAAC,GAC/CA,OAAO,GACPE,SAAS;MACfD,OAAO,EAAEN,cAAc,CAACM,OAAO,CAAC,GAAGA,OAAO,GAAGC;IACjD,CAAC;EACL;EACA,OAAOJ,KAAK,CAACK,OAAO,KAAK,KAAK,GAAGJ,OAAO,GAAG,CAAC,CAAC;AACjD;AAEA,SAASF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}