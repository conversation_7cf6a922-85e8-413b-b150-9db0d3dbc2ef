{"ast": null, "code": "import { animateValue } from './js/index.mjs';\nimport { noop } from '../../utils/noop.mjs';\nfunction createInstantAnimation({\n  keyframes,\n  delay,\n  onUpdate,\n  onComplete\n}) {\n  const setValue = () => {\n    onUpdate && onUpdate(keyframes[keyframes.length - 1]);\n    onComplete && onComplete();\n    /**\n     * TODO: As this API grows it could make sense to always return\n     * animateValue. This will be a bigger project as animateValue\n     * is frame-locked whereas this function resolves instantly.\n     * This is a behavioural change and also has ramifications regarding\n     * assumptions within tests.\n     */\n    return {\n      time: 0,\n      speed: 1,\n      duration: 0,\n      play: noop,\n      pause: noop,\n      stop: noop,\n      then: resolve => {\n        resolve();\n        return Promise.resolve();\n      },\n      cancel: noop,\n      complete: noop\n    };\n  };\n  return delay ? animateValue({\n    keyframes: [0, 1],\n    duration: 0,\n    delay,\n    onComplete: setValue\n  }) : setValue();\n}\nexport { createInstantAnimation };", "map": {"version": 3, "names": ["animateValue", "noop", "createInstantAnimation", "keyframes", "delay", "onUpdate", "onComplete", "setValue", "length", "time", "speed", "duration", "play", "pause", "stop", "then", "resolve", "Promise", "cancel", "complete"], "sources": ["D:/app_dev_aug/jermesa_network/node_modules/framer-motion/dist/es/animation/animators/instant.mjs"], "sourcesContent": ["import { animateValue } from './js/index.mjs';\nimport { noop } from '../../utils/noop.mjs';\n\nfunction createInstantAnimation({ keyframes, delay, onUpdate, onComplete, }) {\n    const setValue = () => {\n        onUpdate && onUpdate(keyframes[keyframes.length - 1]);\n        onComplete && onComplete();\n        /**\n         * TODO: As this API grows it could make sense to always return\n         * animateValue. This will be a bigger project as animateValue\n         * is frame-locked whereas this function resolves instantly.\n         * This is a behavioural change and also has ramifications regarding\n         * assumptions within tests.\n         */\n        return {\n            time: 0,\n            speed: 1,\n            duration: 0,\n            play: (noop),\n            pause: (noop),\n            stop: (noop),\n            then: (resolve) => {\n                resolve();\n                return Promise.resolve();\n            },\n            cancel: (noop),\n            complete: (noop),\n        };\n    };\n    return delay\n        ? animateValue({\n            keyframes: [0, 1],\n            duration: 0,\n            delay,\n            onComplete: setValue,\n        })\n        : setValue();\n}\n\nexport { createInstantAnimation };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,IAAI,QAAQ,sBAAsB;AAE3C,SAASC,sBAAsBA,CAAC;EAAEC,SAAS;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAY,CAAC,EAAE;EACzE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnBF,QAAQ,IAAIA,QAAQ,CAACF,SAAS,CAACA,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC;IACrDF,UAAU,IAAIA,UAAU,CAAC,CAAC;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,OAAO;MACHG,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAGX,IAAK;MACZY,KAAK,EAAGZ,IAAK;MACba,IAAI,EAAGb,IAAK;MACZc,IAAI,EAAGC,OAAO,IAAK;QACfA,OAAO,CAAC,CAAC;QACT,OAAOC,OAAO,CAACD,OAAO,CAAC,CAAC;MAC5B,CAAC;MACDE,MAAM,EAAGjB,IAAK;MACdkB,QAAQ,EAAGlB;IACf,CAAC;EACL,CAAC;EACD,OAAOG,KAAK,GACNJ,YAAY,CAAC;IACXG,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACjBQ,QAAQ,EAAE,CAAC;IACXP,KAAK;IACLE,UAAU,EAAEC;EAChB,CAAC,CAAC,GACAA,QAAQ,CAAC,CAAC;AACpB;AAEA,SAASL,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}